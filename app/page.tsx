"use client";

import AppHeader from "@/components/navigation/AppHeader";
import Footer from "@/components/navigation/Footer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Logo from "@/components/ui/logo";
import { motion } from "framer-motion";
import { ArrowRight, TrendingUp, DollarSign, Target, Bell, BarChart3 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function Home() {
  const [learnMoreHover, setLearnMoreHover] = useState(false);
  const [pricingHover, setPricingHover] = useState(false);
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100/20 to-gray-200/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-200/20 via-transparent to-transparent" />

        <div className="relative max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge variant="secondary" className="mb-6 px-6 py-2 text-sm font-medium bg-gray-100 text-black border-gray-200">
                🚀 10X Growth Hack Program Available
              </Badge>
              <h1 className="text-5xl sm:text-7xl font-bold text-black mb-8 leading-tight">
                Are you
                <span className="block text-black">
                  10x growth ready?
                </span>
              </h1>
              <p className="text-body-large text-gray-600 mb-12 max-w-2xl mx-auto">
                Data-driven financial evaluation platform that automates scoring, provides actionable insights, and connects businesses with the right investors.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/sme">
                  <Button size="lg" className="px-8 py-4 text-button bg-black hover:bg-gray-800 text-white rounded-full">
                    Evaluate My Company
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/investor">
                  <Button variant="outline" size="lg" className="px-8 py-4 text-button border-gray-300 text-black hover:bg-gray-50 rounded-full">
                    View Deal Pipeline
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>

          {/* SME Dashboard Preview */}
          <motion.div
            className="relative max-w-6xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <div className="bg-white backdrop-blur-xl border border-gray-200 rounded-3xl p-8 shadow-2xl">
              <div className="grid lg:grid-cols-4 gap-6">
                {/* Sidebar */}
                <div className="lg:col-span-1 bg-gray-50 rounded-2xl p-6 border border-gray-200">
                  <div className="flex items-center gap-3 mb-8">
                    <Logo size="sm" />
                  </div>

                  

                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="flex items-center gap-2 text-gray-600">
                      <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                      <span className="text-sm">TechCorp Ltd</span>
                    </div>
                  </div>
                </div>
                {/* Main Content */}
                <div className="lg:col-span-3 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-2xl font-bold text-black mb-1">Financial Health Dashboard</h2>
                      <div className="flex items-center gap-4">
                        <span className="text-black text-sm font-medium">TechCorp Ltd</span>
                        <span className="text-gray-500 text-sm">Last updated: Today</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <Bell className="w-5 h-5 text-gray-600" />
                      <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Financial Health Score */}
                    <Card className="bg-white border-gray-200">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-gray-600 text-sm">Financial Health Score</span>
                          <div className="w-2 h-2 bg-black rounded-full"></div>
                        </div>
                        <div className="text-3xl font-bold text-black mb-2">78/100</div>
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-gray-600">Grade: B+</span>
                          <span className="text-gray-400">•</span>
                          <span className="text-black">+5 this month</span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Investor Interest */}
                    <Card className="bg-white border-gray-200">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-gray-600 text-sm">Investor Interest</span>
                          <select className="bg-white text-black text-xs px-2 py-1 rounded border-gray-300">
                            <option>This Month</option>
                          </select>
                        </div>
                        <div className="relative">
                          <div className="w-24 h-24 mx-auto">
                            <div className="w-full h-full rounded-full border-8 border-gray-200 relative">
                              <div className="absolute inset-0 rounded-full border-8 border-black border-t-transparent transform rotate-45"></div>
                            </div>
                          </div>
                          <div className="text-center mt-4">
                            <div className="flex items-center justify-center gap-4 text-xs">
                              <div className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-black rounded-full"></div>
                                <span className="text-gray-600">High Interest</span>
                                <span className="text-black">12</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                <span className="text-gray-600">Watching</span>
                                <span className="text-black">28</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Key Metrics Overview */}
                  <Card className="bg-white border-gray-200">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-6">
                        <span className="text-black text-sm font-medium">Key Financial Metrics</span>
                        <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center">
                          <BarChart3 className="w-4 h-4 text-white" />
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="text-lg font-bold text-black">$3.1M</div>
                          <div className="text-xs text-gray-600">Annual Revenue</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-black">18%</div>
                          <div className="text-xs text-gray-600">Profit Margin</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-black">1.8x</div>
                          <div className="text-xs text-gray-600">Debt-to-Equity</div>
                        </div>
                      </div>
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">10X Growth Eligible</span>
                          <span className="text-black font-medium">✓ Qualified</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Platform Statistics */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 bg-white rounded-3xl py-12 px-8 shadow-lg border border-gray-100"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <div className="text-center">
              <div className="text-5xl font-bold text-black mb-3" style={{ color: '#000000' }}>500+</div>
              <div className="text-gray-600 font-medium">SMEs Evaluated</div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold text-black mb-3" style={{ color: '#000000' }}>$65M+</div>
              <div className="text-gray-600 font-medium">Funding Facilitated</div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold text-black mb-3" style={{ color: '#000000' }}>95%</div>
              <div className="text-gray-600 font-medium">Accuracy Rate</div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Role Selection Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Choose Your Path</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Select your role to access tailored features and insights designed for your specific needs.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* SME Owner */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <TrendingUp className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">SME Owner</h3>
                  <p className="text-gray-600 mb-6">
                    Get your business evaluated and connect with investors through our data-driven platform.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• Automated Financial Health Score</li>
                    <li>• Secure Document Upload</li>
                    <li>• Investor Matching</li>
                  </ul>
                  <Link href="/sme">
                    <Button className="w-full mt-6 bg-black hover:bg-gray-800">
                      Get Started <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>

            {/* Investor */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <DollarSign className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Investor</h3>
                  <p className="text-gray-600 mb-6">
                    Discover and evaluate investment opportunities with comprehensive risk assessment tools.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• Curated Deal Pipeline</li>
                    <li>• Risk Assessment Tools</li>
                    <li>• Performance Analytics</li>
                  </ul>
                  <Link href="/investor">
                    <Button className="w-full mt-6 bg-black hover:bg-gray-800">
                      Explore Deals <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>

            {/* Consultant */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Target className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Consultant</h3>
                  <p className="text-gray-600 mb-6">
                    Help SMEs improve their financial health and earn referral rewards through our platform.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• Client Management Tools</li>
                    <li>• Referral Tracking</li>
                    <li>• Performance Insights</li>
                  </ul>
                  <Link href="/consultant">
                    <Button className="w-full mt-6 bg-black hover:bg-gray-800">
                      Start Consulting <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* 10X Growth Hack CTA Section */}
      <section className="py-20 px-4 bg-black">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🎯 Exclusive Program
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">10X Growth Hack Program</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join our exclusive program and accelerate your business growth with personalized strategies and expert guidance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/10x-growth-hack">
                <Button 
                  size="lg" 
                  className="text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300"
                  style={{ 
                    backgroundColor: learnMoreHover ? '#f3f4f6' : '#ffffff', 
                    color: '#000000',
                    border: 'none',
                    transform: learnMoreHover ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onMouseEnter={() => setLearnMoreHover(true)}
                  onMouseLeave={() => setLearnMoreHover(false)}
                >
                  <span style={{ color: '#000000' }}>Learn More</span>
                  <ArrowRight className="ml-2 h-5 w-5" style={{ color: '#000000' }} />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300"
                  style={{
                    backgroundColor: pricingHover ? '#ffffff' : 'transparent',
                    color: pricingHover ? '#000000' : '#ffffff',
                    border: '2px solid #ffffff',
                    transform: pricingHover ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onMouseEnter={() => setPricingHover(true)}
                  onMouseLeave={() => setPricingHover(false)}
                >
                  <span style={{ color: pricingHover ? '#000000' : '#ffffff' }}>View Pricing</span>
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}