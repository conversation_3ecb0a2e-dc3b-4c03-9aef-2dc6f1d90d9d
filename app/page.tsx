"use client";

import AppHeader from "@/components/navigation/AppHeader";
import Footer from "@/components/navigation/Footer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRight, DollarSign, Target, TrendingUp } from "lucide-react";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100/20 to-gray-200/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-200/20 via-transparent to-transparent" />

        <div className="relative max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge variant="secondary" className="mb-6 px-6 py-2 text-sm font-medium bg-gray-100 text-black border-gray-200">
                🚀 10X Growth Hack Program Available
              </Badge>
              <h1 className="text-5xl sm:text-7xl font-bold text-black mb-8 leading-tight">
                Are you
                <span className="block text-black">
                  10x growth ready?
                </span>
              </h1>
              <p className="text-body-large text-gray-600 mb-12 max-w-2xl mx-auto">
              Data-driven financial intelligence platform that evaluates SMEs, scores their growth potential, and connects them with the right investors for funding and scale.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/sme">
                  <Button size="lg" className="px-8 py-4 text-button bg-black hover:bg-gray-800 text-white rounded-full">
                    Evaluate My Company
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/investor">
                  <Button variant="outline" size="lg" className="px-8 py-4 text-button border-gray-300 text-black hover:bg-gray-50 rounded-full">
                    View Deal Pipeline
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>

          {/* Growth Journey Cards */}
          <motion.div
            className="relative max-w-6xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* From Profitable to Powerful */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="group"
              >
                <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300 hover:shadow-xl">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                      <TrendingUp className="w-8 h-8 text-black" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">From Profitable</h3>
                    <h3 className="text-2xl font-bold text-black mb-6">to Powerful</h3>
                    <p className="text-gray-600 leading-relaxed">
                      Transform your profitable business into a market-dominating powerhouse with strategic growth acceleration.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* From Distress to Dominance */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="group"
              >
                <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300 hover:shadow-xl">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                      <Target className="w-8 h-8 text-black" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">From Distress</h3>
                    <h3 className="text-2xl font-bold text-black mb-6">to Dominance</h3>
                    <p className="text-gray-600 leading-relaxed">
                      Turn financial challenges into competitive advantages and emerge as an industry leader.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Your 10x Growth Starts Here */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="group"
              >
                <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300 hover:shadow-xl">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                      <DollarSign className="w-8 h-8 text-black" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">Your 10x Growth</h3>
                    <h3 className="text-2xl font-bold text-black mb-6">Starts Here</h3>
                    <p className="text-gray-600 leading-relaxed">
                      Begin your exponential growth journey with data-driven insights and expert guidance.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </motion.div>

          {/* Platform Statistics */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 bg-white rounded-3xl py-12 px-8 shadow-lg border border-gray-100"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <div className="text-center">
              <div className="text-5xl font-bold text-black mb-3" style={{ color: '#000000' }}>500+</div>
              <div className="text-gray-600 font-medium">SMEs Evaluated</div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold text-black mb-3" style={{ color: '#000000' }}>$65M+</div>
              <div className="text-gray-600 font-medium">Funding Facilitated</div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold text-black mb-3" style={{ color: '#000000' }}>95%</div>
              <div className="text-gray-600 font-medium">Accuracy Rate</div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Role Selection Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Choose Your Path</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Select your role to access tailored features and insights designed for your specific needs.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* SME Owner */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <TrendingUp className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">SME Owner</h3>
                  <p className="text-gray-600 mb-6">
                    Get your business evaluated and connect with investors through our data-driven platform.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• AI powered Financial Health Score</li>
                    <li>• Secure Document Upload</li>
                    <li>• Capital Raise</li>
                  </ul>
                  <Link href="/sme">
                    <Button className="w-full mt-6 bg-black hover:bg-gray-800">
                      Get Started <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>

            {/* Investor */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <DollarSign className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Investor</h3>
                  <p className="text-gray-600 mb-6">
                    Discover and evaluate investment opportunities with comprehensive risk assessment tools.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• Curated Deal Pipeline</li>
                    <li>• Risk Assessment Tools</li>
                    <li>• Performance Analytics</li>
                  </ul>
                  <Link href="/investor">
                    <Button className="w-full mt-6 bg-black hover:bg-gray-800">
                      Explore Deals <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>

            {/* Consultant */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Target className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Consultant</h3>
                  <p className="text-gray-600 mb-6">
                    Empower SMEs to strengthen their financial health while earning premium rewards for every successful referral and impactful connection you make.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• Client Management Tools</li>
                    <li>• Referral Tracking</li>
                    <li>• Performance Insights</li>
                  </ul>
                  <Link href="/consultant">
                    <Button className="w-full mt-6 bg-black hover:bg-gray-800">
                      Start Consulting <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* 10X Growth Hack CTA Section */}
      <section className="py-20 px-4 bg-black">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🎯 Exclusive Program
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">10X Growth Hack Program</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join our exclusive program and accelerate your business growth with personalized strategies and expert guidance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/10x-growth-hack">
                <Button size="lg" className="text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300 !bg-white !text-black hover:!bg-gray-100">
                  Learn More
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button variant="outline" size="lg" className="border-2 border-white !text-black !bg-white hover:!bg-gray-100 hover:!text-black text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300">
                  View Pricing
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}