"use client";

import AppHeader from "@/components/navigation/AppHeader";
import Footer from "@/components/navigation/Footer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRight, BarChart3, Check, FileText, Search, Target, TrendingUp, Upload, Users } from "lucide-react";
import Link from "next/link";

export default function SMEPage() {
  const benefits = [
    {
      icon: TrendingUp,
      title: "Automated Financial Health Score",
      description: "Get instant evaluation of your business financial health with our AI-powered scoring system.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: Upload,
      title: "Secure Document Upload",
      description: "Upload financial statements, tax returns, and bank statements with enterprise-grade security.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: FileText,
      title: "Detailed Analysis Reports",
      description: "Receive comprehensive reports with actionable insights and improvement recommendations.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: Users,
      title: "Investor Matching",
      description: "Connect with pre-qualified investors who are interested in businesses like yours.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: Search,
      title: "Data Privacy & Security",
      description: "Your sensitive business data is protected with bank-level encryption and security measures.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: BarChart3,
      title: "10X Growth Hack Access",
      description: "Exclusive access to our premium growth acceleration program with expert mentorship.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
  ];

  const steps = [
    {
      step: "01",
      title: "Create Account",
      description: "Sign up and complete your business profile with basic information.",
    },
    {
      step: "02",
      title: "Upload Documents",
      description: "Securely upload your financial statements and business documents.",
    },
    {
      step: "03",
      title: "Get Analysis",
      description: "Receive your automated financial health score and detailed insights.",
    },
    {
      step: "04",
      title: "Connect with Investors",
      description: "Get matched with relevant investors and start conversations.",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100/20 to-gray-200/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-200/20 via-transparent to-transparent" />
        
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge variant="secondary" className="mb-6 px-6 py-2 text-sm font-medium bg-gray-100 text-black border-gray-200">
              🚀 For SME Owners
            </Badge>
            <h1 className="text-5xl sm:text-7xl font-bold text-black mb-8 leading-tight">
              Get Your Business
              <span className="block text-black">
                Investment Ready
              </span>
            </h1>
            <p className="text-body-large text-gray-600 mb-12 max-w-3xl mx-auto">
              Transform your business with data-driven insights, automated financial scoring,
              and direct access to a curated network of investors.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/sme/signup">
                <Button size="lg" className="px-8 py-4 text-button bg-black hover:bg-gray-800 text-white rounded-full">
                  Start Free Analysis <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button variant="outline" size="lg" className="px-8 py-4 text-button border-gray-300 text-black hover:bg-gray-50 rounded-full">
                  View Sample Report
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Why Choose 10xCFO for Your SME?</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Our platform is specifically designed to help small and medium enterprises access funding
              and grow their business with data-driven insights.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className={`w-16 h-16 ${benefit.bgColor} rounded-2xl flex items-center justify-center mb-6`}>
                      <benefit.icon className={`w-8 h-8 ${benefit.color}`} />
                    </div>
                    <h3 className="text-xl font-bold text-black mb-4">{benefit.title}</h3>
                    <p className="text-gray-600">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>How It Works</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Get started in just 4 simple steps and unlock your business potential.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto bg-white rounded-3xl py-12 px-8 shadow-lg border border-gray-100">
            {steps.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-black mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Choose Your Plan</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Start free and upgrade as your business grows. All plans include our core evaluation features.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Free Plan */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Check className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Starter</h3>
                  <p className="text-gray-600 mb-6">
                    Perfect for getting started with basic financial evaluation.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600 mb-6">
                    <li>• Basic financial health score</li>
                    <li>• Document upload (up to 5 files)</li>
                    <li>• Basic report generation</li>
                    <li>• Email support</li>
                  </ul>
                  <Button className="w-full mt-6 bg-black hover:bg-gray-800 text-white">
                    Get Started <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Pro Plan */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-white border-gray-300 hover:border-gray-400 transition-all duration-300 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-black text-white px-4 py-1">Most Popular</Badge>
                </div>
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <TrendingUp className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Professional</h3>
                  <p className="text-gray-600 mb-6">
                    For growing businesses with advanced needs and investor matching.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600 mb-6">
                    <li>• Advanced financial analysis</li>
                    <li>• Unlimited document uploads</li>
                    <li>• Detailed reports & insights</li>
                    <li>• Investor matching</li>
                    <li>• Priority support</li>
                  </ul>
                  <Button className="w-full mt-6 bg-black hover:bg-gray-800 text-white">
                    Start Pro Trial <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Premium Plan */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Target className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Enterprise</h3>
                  <p className="text-gray-600 mb-6">
                    For established businesses with comprehensive growth and advisory needs.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600 mb-6">
                    <li>• Everything in Professional</li>
                    <li>• 10X Growth Hack access</li>
                    <li>• Dedicated advisor calls</li>
                    <li>• Custom integrations</li>
                    <li>• White-label reports</li>
                  </ul>
                  <Button className="w-full mt-6 bg-black hover:bg-gray-800 text-white">
                    Contact Sales <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-black">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🎯 Ready to Get Started?
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">Transform Your Business Today</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join hundreds of SMEs who have already improved their financial health and connected with investors.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/sme/signup">
                <Button size="lg" className="text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300 !bg-white !text-black hover:!bg-gray-100">
                  Get Started Free <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link href="/10x-growth-hack">
                <Button variant="outline" size="lg" className="border-2 border-white !text-black !bg-white hover:!bg-gray-100 hover:!text-black text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300">
                  Learn About 10X Growth
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
