"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import {
    Award,
    BarChart3,
    BookOpen,
    Calendar,
    CheckCircle,
    Clock,
    FileText,
    Lock,
    Play,
    Star,
    Target,
    TrendingUp,
    Users,
    Video
} from "lucide-react";
import { useState } from "react";

interface ProgramModule {
  id: number;
  title: string;
  description: string;
  duration: string;
  status: "completed" | "current" | "locked";
  progress: number;
  lessons: number;
  completedLessons: number;
  type: "video" | "workshop" | "assignment" | "assessment";
}

interface Milestone {
  id: number;
  title: string;
  description: string;
  target: string;
  current: string;
  progress: number;
  status: "completed" | "in-progress" | "pending";
  reward: string;
}

export default function SME10xGrowthProgram() {
  const [activeTab, setActiveTab] = useState("dashboard");

  // Mock user progress data
  const userProgress = {
    overallProgress: 45,
    completedModules: 3,
    totalModules: 8,
    currentStreak: 7,
    totalPoints: 1850,
    level: "Growth Accelerator",
    nextMilestone: "Revenue Optimization",
    programStartDate: "2024-01-15",
    estimatedCompletion: "2024-04-15"
  };

  const modules: ProgramModule[] = [
    {
      id: 1,
      title: "Financial Foundation Mastery",
      description: "Master cash flow management, financial planning, and key metrics tracking",
      duration: "2 weeks",
      status: "completed",
      progress: 100,
      lessons: 8,
      completedLessons: 8,
      type: "video"
    },
    {
      id: 2,
      title: "Market Analysis & Positioning",
      description: "Deep dive into market research, competitive analysis, and strategic positioning",
      duration: "2 weeks",
      status: "completed",
      progress: 100,
      lessons: 10,
      completedLessons: 10,
      type: "workshop"
    },
    {
      id: 3,
      title: "Digital Marketing Acceleration",
      description: "Scale your customer acquisition with proven digital marketing strategies",
      duration: "3 weeks",
      status: "current",
      progress: 60,
      lessons: 12,
      completedLessons: 7,
      type: "video"
    },
    {
      id: 4,
      title: "Operations & Process Optimization",
      description: "Streamline operations for maximum efficiency and scalability",
      duration: "2 weeks",
      status: "locked",
      progress: 0,
      lessons: 8,
      completedLessons: 0,
      type: "assignment"
    },
    {
      id: 5,
      title: "Team Building & Leadership",
      description: "Build high-performing teams and develop leadership skills",
      duration: "3 weeks",
      status: "locked",
      progress: 0,
      lessons: 15,
      completedLessons: 0,
      type: "workshop"
    },
    {
      id: 6,
      title: "Advanced Growth Strategies",
      description: "Implement advanced growth hacking and scaling techniques",
      duration: "3 weeks",
      status: "locked",
      progress: 0,
      lessons: 14,
      completedLessons: 0,
      type: "video"
    }
  ];

  const milestones: Milestone[] = [
    {
      id: 1,
      title: "Revenue Growth",
      description: "Achieve 30% revenue increase from baseline",
      target: "$500K",
      current: "$385K",
      progress: 77,
      status: "in-progress",
      reward: "Growth Champion Badge + $500 bonus"
    },
    {
      id: 2,
      title: "Customer Acquisition",
      description: "Reach 1000 active customers",
      target: "1000",
      current: "650",
      progress: 65,
      status: "in-progress",
      reward: "Customer Master Certificate"
    },
    {
      id: 3,
      title: "Operational Efficiency",
      description: "Reduce operational costs by 20%",
      target: "20%",
      current: "12%",
      progress: 60,
      status: "in-progress",
      reward: "Efficiency Expert Badge"
    }
  ];

  const upcomingSessions = [
    {
      id: 1,
      title: "1-on-1 Growth Coaching",
      date: "Tomorrow",
      time: "2:00 PM",
      type: "coaching",
      mentor: "Sarah Johnson"
    },
    {
      id: 2,
      title: "Digital Marketing Workshop",
      date: "Friday",
      time: "10:00 AM",
      type: "workshop",
      mentor: "Group Session"
    },
    {
      id: 3,
      title: "Peer Mastermind Session",
      date: "Next Monday",
      time: "3:00 PM",
      type: "mastermind",
      mentor: "Peer Group"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "current": case "in-progress": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "locked": case "pending": return "bg-slate-500/20 text-slate-400 border-slate-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video": return <Video className="w-4 h-4" />;
      case "workshop": return <Users className="w-4 h-4" />;
      case "assignment": return <FileText className="w-4 h-4" />;
      case "assessment": return <CheckCircle className="w-4 h-4" />;
      default: return <BookOpen className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <AppHeader variant="sme" />
      
      {/* Back Navigation */}
      <BackButton href="/sme/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Program Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-3xl font-bold text-white">10X Growth Program</h1>
              <Badge className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border-yellow-500/30">
                <Star className="w-4 h-4 mr-1" />
                {userProgress.level}
              </Badge>
            </div>
            <p className="text-slate-400">Transform your business with our comprehensive growth acceleration program</p>
          </div>
          <div className="text-right">
            <div className="text-h2 text-white">{userProgress.overallProgress}%</div>
            <div className="text-body-small text-slate-400">Program Complete</div>
          </div>
        </div>

        {/* Progress Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 border-blue-500/30">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                <div className="text-center">
                  <div className="text-h1 text-white mb-1">{userProgress.completedModules}/{userProgress.totalModules}</div>
                  <div className="text-body-small text-slate-300">Modules</div>
                </div>
                <div className="text-center">
                  <div className="text-h1 text-white mb-1">{userProgress.currentStreak}</div>
                  <div className="text-body-small text-slate-300">Day Streak</div>
                </div>
                <div className="text-center">
                  <div className="text-h1 text-white mb-1">{userProgress.totalPoints}</div>
                  <div className="text-body-small text-slate-300">Points</div>
                </div>
                <div className="text-center">
                  <div className="text-h1 text-white mb-1">12</div>
                  <div className="text-body-small text-slate-300">Weeks Left</div>
                </div>
                <div className="text-center">
                  <div className="text-h1 text-white mb-1">A+</div>
                  <div className="text-body-small text-slate-300">Grade</div>
                </div>
              </div>
              <div className="mt-6">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-slate-300">Overall Progress</span>
                  <span className="text-slate-300">{userProgress.overallProgress}%</span>
                </div>
                <Progress value={userProgress.overallProgress} className="h-3" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-slate-800/50 rounded-lg p-1">
          {[
            { id: "dashboard", label: "Dashboard", icon: BarChart3 },
            { id: "modules", label: "Learning Path", icon: BookOpen },
            { id: "milestones", label: "Milestones", icon: Target },
            { id: "community", label: "Community", icon: Users }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors flex-1 justify-center ${
                activeTab === tab.id
                  ? "bg-blue-600 text-white"
                  : "text-slate-300 hover:text-white hover:bg-slate-700/50"
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="text-sm font-medium">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        {activeTab === "dashboard" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Current Focus */}
            <div className="lg:col-span-2 space-y-6">
              {/* Continue Learning */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Play className="w-5 h-5 mr-2" />
                    Continue Learning
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-slate-700/50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-white font-semibold">Digital Marketing Acceleration</h3>
                      <Badge className={getStatusColor("current")}>In Progress</Badge>
                    </div>
                    <p className="text-slate-400 text-sm mb-4">
                      Module 3: Advanced Customer Acquisition Strategies
                    </p>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-slate-300 text-sm">Progress: 7/12 lessons</span>
                      <span className="text-slate-300 text-sm">60% complete</span>
                    </div>
                    <Progress value={60} className="mb-4 h-2" />
                    <div className="flex gap-3">
                      <Button className="flex-1">Continue Lesson</Button>
                      <Button variant="outline" className="border-slate-600 text-slate-300">
                        <Calendar className="w-4 h-4 mr-2" />
                        Schedule
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Achievements */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Award className="w-5 h-5 mr-2" />
                    Recent Achievements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { title: "Market Analysis Expert", date: "2 days ago", points: 250 },
                      { title: "7-Day Learning Streak", date: "Today", points: 100 },
                      { title: "Financial Foundation Complete", date: "1 week ago", points: 500 }
                    ].map((achievement, index) => (
                      <div key={index} className="flex items-center justify-between bg-slate-700/30 rounded-lg p-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                            <Award className="w-5 h-5 text-yellow-400" />
                          </div>
                          <div>
                            <div className="text-white font-medium">{achievement.title}</div>
                            <div className="text-slate-400 text-sm">{achievement.date}</div>
                          </div>
                        </div>
                        <div className="text-yellow-400 font-semibold">+{achievement.points} pts</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Upcoming Sessions */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white text-lg">Upcoming Sessions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {upcomingSessions.map((session) => (
                      <div key={session.id} className="bg-slate-700/50 rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <Video className="w-4 h-4 text-blue-400" />
                          <span className="text-white text-sm font-medium">{session.title}</span>
                        </div>
                        <div className="text-slate-400 text-xs">{session.date} at {session.time}</div>
                        <div className="text-slate-400 text-xs">with {session.mentor}</div>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" size="sm">
                    <Calendar className="w-4 h-4 mr-2" />
                    View All Sessions
                  </Button>
                </CardContent>
              </Card>

              {/* Next Milestone */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white text-lg">Next Milestone</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Target className="w-8 h-8 text-yellow-400" />
                    </div>
                    <h3 className="text-white font-semibold mb-2">{userProgress.nextMilestone}</h3>
                    <p className="text-slate-400 text-sm mb-4">Complete 2 more modules to unlock</p>
                    <Progress value={75} className="h-2 mb-3" />
                    <div className="text-slate-300 text-sm">75% Progress</div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white text-lg">This Week</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-blue-400" />
                        <span className="text-slate-300 text-sm">Learning Hours</span>
                      </div>
                      <span className="text-white font-semibold">8.5h</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        <span className="text-slate-300 text-sm">Lessons Completed</span>
                      </div>
                      <span className="text-white font-semibold">12</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="w-4 h-4 text-purple-400" />
                        <span className="text-slate-300 text-sm">Points Earned</span>
                      </div>
                      <span className="text-white font-semibold">450</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {activeTab === "modules" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {modules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-colors">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                          {getTypeIcon(module.type)}
                        </div>
                        <div>
                          <CardTitle className="text-white text-lg">{module.title}</CardTitle>
                          <p className="text-slate-400 text-sm mt-1">{module.description}</p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(module.status)}>
                        {module.status === "current" ? "In Progress" : 
                         module.status === "locked" ? <Lock className="w-3 h-3" /> : 
                         module.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-400">Progress</span>
                        <span className="text-slate-300">{module.completedLessons}/{module.lessons} lessons</span>
                      </div>
                      <Progress value={module.progress} className="h-2" />
                      
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-slate-400" />
                          <span className="text-slate-400">{module.duration}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(module.type)}
                          <span className="text-slate-400 capitalize">{module.type}</span>
                        </div>
                      </div>

                      <Button 
                        className="w-full" 
                        disabled={module.status === "locked"}
                        variant={module.status === "locked" ? "outline" : "default"}
                      >
                        {module.status === "completed" ? "Review Module" : 
                         module.status === "current" ? "Continue Module" : 
                         "Locked"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {activeTab === "milestones" && (
          <div className="space-y-6">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-white font-semibold text-lg">{milestone.title}</h3>
                        <p className="text-slate-400">{milestone.description}</p>
                      </div>
                      <Badge className={getStatusColor(milestone.status)}>
                        {milestone.status === "in-progress" ? "In Progress" : milestone.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">{milestone.current}</div>
                        <div className="text-slate-400 text-sm">Current</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">{milestone.progress}%</div>
                        <div className="text-slate-400 text-sm">Progress</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">{milestone.target}</div>
                        <div className="text-slate-400 text-sm">Target</div>
                      </div>
                    </div>
                    
                    <Progress value={milestone.progress} className="h-3 mb-4" />
                    
                    <div className="bg-slate-700/30 rounded-lg p-3">
                      <div className="flex items-center space-x-2">
                        <Award className="w-4 h-4 text-yellow-400" />
                        <span className="text-slate-300 text-sm font-medium">Reward:</span>
                        <span className="text-yellow-400 text-sm">{milestone.reward}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {activeTab === "community" && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Peer Network</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <Users className="w-16 h-16 text-slate-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">Connect with Fellow Entrepreneurs</h3>
                    <p className="text-slate-400 mb-4">Join mastermind groups, share experiences, and learn from peers</p>
                    <Button>Join Community</Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Expert Mentorship</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <Video className="w-16 h-16 text-slate-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">1-on-1 Coaching Sessions</h3>
                    <p className="text-slate-400 mb-4">Get personalized guidance from industry experts and successful entrepreneurs</p>
                    <Button>Schedule Session</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
