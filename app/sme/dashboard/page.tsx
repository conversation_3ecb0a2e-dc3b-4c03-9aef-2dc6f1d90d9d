"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    BarChart3,
    Check,
    Clock,
    FileText,
    Target,
    TrendingUp,
    Upload,
    Users
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function SMEDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.push('/');
  };
  // Mock data - in real app, this would come from API
  const financialScore = 78;
  const documentsUploaded = 8;
  const totalDocuments = 12;
  const investorInterest = 15;
  const profileCompletion = 85;

  const recentActivity = [
    { id: 1, type: "document", message: "Financial statements uploaded", time: "2 hours ago", status: "success" },
    { id: 2, type: "investor", message: "3 new investors viewed your profile", time: "5 hours ago", status: "info" },
    { id: 3, type: "score", message: "Financial health score improved by 5 points", time: "1 day ago", status: "success" },
    { id: 4, type: "meeting", message: "Advisor call scheduled for tomorrow", time: "2 days ago", status: "pending" }
  ];

  const actionItems = [
    { id: 1, task: "Upload bank statements (last 12 months)", priority: "high", completed: false },
    { id: 2, task: "Complete business profile information", priority: "medium", completed: false },
    { id: 3, task: "Schedule advisor consultation call", priority: "low", completed: true },
    { id: 4, task: "Review investor interest notifications", priority: "medium", completed: false }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <AppHeader variant="sme" />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-h1 text-white mb-2">Welcome back, {user?.companyName || user?.name || 'User'}</h1>
              <p className="text-body text-slate-400">Here's your business performance overview</p>
            </div>
            <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
              Profile {profileCompletion}% Complete
            </Badge>
          </div>
        </motion.div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Financial Health Score */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-blue-400" />
                  </div>
                  <Badge className="bg-emerald-500/20 text-emerald-400 text-xs">+5 this month</Badge>
                </div>
                <div className="text-h2 text-white mb-1">{financialScore}/100</div>
                <p className="text-body-small text-slate-400">Financial Health Score</p>
                <div className="mt-3">
                  <Progress value={financialScore} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Documents Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-emerald-400" />
                  </div>
                  <Link href="/sme/dashboard/upload">
                    <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300 p-0">
                      <Upload className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
                <div className="text-h2 text-white mb-1">{documentsUploaded}/{totalDocuments}</div>
                <p className="text-body-small text-slate-400">Documents Uploaded</p>
                <div className="mt-3">
                  <Progress value={(documentsUploaded / totalDocuments) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Investor Interest */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-amber-500/20 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-amber-400" />
                  </div>
                  <Badge className="bg-amber-500/20 text-amber-400 text-xs">+3 today</Badge>
                </div>
                <div className="text-h2 text-white mb-1">{investorInterest}</div>
                <p className="text-body-small text-slate-400">Investor Views</p>
                <div className="mt-3 flex items-center text-amber-400 text-xs">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  High interest level
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* 10X Growth Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <Target className="w-6 h-6 text-purple-400" />
                  </div>
                  <Badge className="bg-emerald-500/20 text-emerald-400 text-xs">Enrolled</Badge>
                </div>
                <div className="text-h5 text-white mb-1">10X Growth</div>
                <p className="text-body-small text-slate-400">45% Complete</p>
                <div className="mt-3">
                  <Link href="/sme/dashboard/10x-growth">
                    <Button size="sm" className="bg-purple-600 hover:bg-purple-700 text-xs">
                      Enter Program
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Action Items */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Check className="w-5 h-5 mr-2 text-emerald-400" />
                  Action Items
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Complete these tasks to improve your financial health score
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {actionItems.map((item) => (
                    <div
                      key={item.id}
                      className={`flex items-center justify-between p-4 rounded-lg border ${
                        item.completed
                          ? "bg-emerald-500/10 border-emerald-500/30"
                          : "bg-slate-700/30 border-slate-600/30"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        {item.completed ? (
                          <Check className="w-5 h-5 text-emerald-400" />
                        ) : (
                          <div className={`w-5 h-5 rounded-full border-2 ${
                            item.priority === "high" ? "border-red-400" :
                            item.priority === "medium" ? "border-amber-400" : "border-slate-400"
                          }`} />
                        )}
                        <div>
                          <p className={`text-label ${item.completed ? "text-emerald-400" : "text-white"}`}>
                            {item.task}
                          </p>
                          <p className="text-caption text-slate-400 capitalize">
                            {item.priority} priority
                          </p>
                        </div>
                      </div>
                      {!item.completed && (
                        <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                          <ArrowRight className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Clock className="w-5 h-5 mr-2 text-blue-400" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.status === "success" ? "bg-emerald-400" :
                        activity.status === "info" ? "bg-blue-400" : "bg-amber-400"
                      }`} />
                      <div className="flex-1">
                        <p className="text-white text-sm">{activity.message}</p>
                        <p className="text-slate-400 text-xs">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mt-8"
        >
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Link href="/sme/dashboard/upload">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 justify-start">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Documents
                  </Button>
                </Link>
                <Link href="/sme/dashboard/reports">
                  <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700 justify-start">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    View Reports
                  </Button>
                </Link>
                <Link href="/sme/dashboard/advisor-call">
                  <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700 justify-start">
                    <Users className="w-4 h-4 mr-2" />
                    Schedule Call
                  </Button>
                </Link>
                <Link href="/sme/dashboard/10x-growth">
                  <Button variant="outline" className="w-full border-purple-600 text-purple-400 hover:bg-purple-600/10 justify-start">
                    <Target className="w-4 h-4 mr-2" />
                    10X Growth Program
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
