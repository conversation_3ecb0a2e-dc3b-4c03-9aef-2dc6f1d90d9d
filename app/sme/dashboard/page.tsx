"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    BarChart3,
    Check,
    Clock,
    FileText,
    Target,
    TrendingUp,
    Upload,
    Users
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function SMEDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.push('/');
  };
  // Mock data - in real app, this would come from API
  const financialScore = 78;
  const documentsUploaded = 8;
  const totalDocuments = 12;
  const investorInterest = 15;
  const profileCompletion = 85;

  const recentActivity = [
    { id: 1, type: "document", message: "Financial statements uploaded", time: "2 hours ago", status: "success" },
    { id: 2, type: "investor", message: "3 new investors viewed your profile", time: "5 hours ago", status: "info" },
    { id: 3, type: "score", message: "Financial health score improved by 5 points", time: "1 day ago", status: "success" },
    { id: 4, type: "meeting", message: "Advisor call scheduled for tomorrow", time: "2 days ago", status: "pending" }
  ];

  const actionItems = [
    { id: 1, task: "Upload bank statements (last 12 months)", priority: "high", completed: false },
    { id: 2, task: "Complete business profile information", priority: "medium", completed: false },
    { id: 3, task: "Schedule advisor consultation call", priority: "low", completed: true },
    { id: 4, task: "Review investor interest notifications", priority: "medium", completed: false }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="sme" />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-h1 text-black mb-2">Welcome back, {user?.companyName || user?.name || 'User'}</h1>
              <p className="text-body text-gray-600">Here's your business performance overview</p>
            </div>
            <Badge className="bg-black text-white border-black">
              Profile {profileCompletion}% Complete
            </Badge>
          </div>
        </motion.div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Financial Health Score */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-black" />
                  </div>
                  <Badge className="bg-black text-white text-xs">+5 this month</Badge>
                </div>
                <div className="text-h2 text-black mb-1">{financialScore}/100</div>
                <p className="text-body-small text-gray-600">Financial Health Score</p>
                <div className="mt-3">
                  <Progress value={financialScore} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Documents Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-black" />
                  </div>
                  <Link href="/sme/dashboard/upload">
                    <Button size="sm" variant="ghost" className="text-black hover:text-gray-600 p-0">
                      <Upload className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
                <div className="text-h2 text-black mb-1">{documentsUploaded}/{totalDocuments}</div>
                <p className="text-body-small text-gray-600">Documents Uploaded</p>
                <div className="mt-3">
                  <Progress value={(documentsUploaded / totalDocuments) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Investor Interest */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-black" />
                  </div>
                  <Badge className="bg-black text-white text-xs">+3 today</Badge>
                </div>
                <div className="text-h2 text-black mb-1">{investorInterest}</div>
                <p className="text-body-small text-gray-600">Investor Views</p>
                <div className="mt-3 flex items-center text-black text-xs">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  High interest level
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* 10X Growth Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Target className="w-6 h-6 text-black" />
                  </div>
                  <Badge className="bg-black text-white text-xs">Enrolled</Badge>
                </div>
                <div className="text-h5 text-black mb-1">10X Growth</div>
                <p className="text-body-small text-gray-600">45% Complete</p>
                <div className="mt-3">
                  <Link href="/sme/dashboard/10x-growth">
                    <Button size="sm" className="bg-black hover:bg-gray-800 text-white text-xs">
                      Enter Program
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Action Items */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Check className="w-5 h-5 mr-2 text-black" />
                  Action Items
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Complete these tasks to improve your financial health score
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {actionItems.map((item) => (
                    <div
                      key={item.id}
                      className={`flex items-center justify-between p-4 rounded-lg border ${
                        item.completed
                          ? "bg-gray-50 border-gray-200"
                          : "bg-gray-50 border-gray-200"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        {item.completed ? (
                          <Check className="w-5 h-5 text-black" />
                        ) : (
                          <div className={`w-5 h-5 rounded-full border-2 ${
                            item.priority === "high" ? "border-red-500" :
                            item.priority === "medium" ? "border-amber-500" : "border-gray-400"
                          }`} />
                        )}
                        <div>
                          <p className={`text-label ${item.completed ? "text-black" : "text-black"}`}>
                            {item.task}
                          </p>
                          <p className="text-caption text-gray-600 capitalize">
                            {item.priority} priority
                          </p>
                        </div>
                      </div>
                      {!item.completed && (
                        <Button size="sm" variant="outline" className="border-gray-300 text-black hover:bg-gray-50">
                          <ArrowRight className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Clock className="w-5 h-5 mr-2 text-black" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.status === "success" ? "bg-green-500" :
                        activity.status === "info" ? "bg-blue-500" : "bg-amber-500"
                      }`} />
                      <div className="flex-1">
                        <p className="text-black text-sm">{activity.message}</p>
                        <p className="text-gray-600 text-xs">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mt-8"
        >
          <Card className="bg-white border-gray-200 shadow-lg">
            <CardHeader>
              <CardTitle className="text-black">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Link href="/sme/dashboard/upload">
                  <div className="w-full bg-black hover:bg-gray-800 text-white rounded-md px-4 py-2 flex items-center cursor-pointer transition-colors">
                    <Upload className="w-4 h-4 mr-2 text-white" />
                    Upload Documents
                  </div>
                </Link>
                <Link href="/sme/dashboard/reports">
                  <div className="w-full border border-gray-300 text-black hover:bg-black hover:text-white hover:border-black bg-white rounded-md px-4 py-2 flex items-center cursor-pointer transition-colors group">
                    <BarChart3 className="w-4 h-4 mr-2 text-black group-hover:text-white" />
                    View Reports
                  </div>
                </Link>
                <Link href="/sme/dashboard/advisor-call">
                  <div className="w-full border border-gray-300 text-black hover:bg-black hover:text-white hover:border-black bg-white rounded-md px-4 py-2 flex items-center cursor-pointer transition-colors group">
                    <Users className="w-4 h-4 mr-2 text-black group-hover:text-white" />
                    Schedule Call
                  </div>
                </Link>
                <Link href="/sme/dashboard/10x-growth">
                  <div className="w-full border border-gray-300 text-black hover:bg-black hover:text-white hover:border-black bg-white rounded-md px-4 py-2 flex items-center cursor-pointer transition-colors group">
                    <Target className="w-4 h-4 mr-2 text-black group-hover:text-white" />
                    10X Growth Program
                  </div>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
