"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Logo from "@/components/ui/logo";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight, DollarSign, Mail, Phone, Target, User } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function InvestorSignup() {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;
  
  const [formData, setFormData] = useState({
    // Personal Info
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    
    // Investment Profile
    investorType: "",
    investmentCapacity: "",
    minInvestment: "",
    maxInvestment: "",
    riskTolerance: "",
    
    // Preferences
    preferredIndustries: [] as string[],
    preferredStages: [] as string[],
    geographicPreference: "",
    investmentHorizon: "",
    expectedReturns: ""
  });

  const updateFormData = (field: string, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const toggleArrayValue = (field: string, value: string) => {
    const currentArray = formData[field as keyof typeof formData] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFormData(field, newArray);
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = (currentStep / totalSteps) * 100;

  const industries = [
    "Technology", "Healthcare", "Finance", "Manufacturing", "Retail", 
    "Energy", "Education", "Real Estate", "Agriculture", "Transportation"
  ];

  const stages = [
    "Seed Stage", "Early Stage", "Growth Stage", "Expansion Stage", "Mature Stage"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Logo href="/investor" size="sm" />
          <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
            Investor Registration
          </Badge>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Progress Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex justify-center mb-6">
            <Logo size="lg" showText={false} />
          </div>
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-white">Investor Registration</h1>
            <span className="text-slate-400">Step {currentStep} of {totalSteps}</span>
          </div>
          <Progress value={progress} className="h-2 mb-2" />
          <p className="text-slate-400 text-sm">Join our network of qualified investors</p>
        </motion.div>

        {/* Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                {currentStep === 1 && <><User className="w-5 h-5 mr-2 text-emerald-400" />Personal Information</>}
                {currentStep === 2 && <><DollarSign className="w-5 h-5 mr-2 text-blue-400" />Investment Profile</>}
                {currentStep === 3 && <><Target className="w-5 h-5 mr-2 text-amber-400" />Investment Preferences</>}
              </CardTitle>
              <CardDescription className="text-slate-400">
                {currentStep === 1 && "Tell us about yourself"}
                {currentStep === 2 && "Define your investment capacity and risk profile"}
                {currentStep === 3 && "Set your investment preferences and criteria"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Step 1: Personal Information */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-slate-300">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData("firstName", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-slate-300">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData("lastName", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="Doe"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-slate-300">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateFormData("email", e.target.value)}
                        className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-slate-300">Phone Number *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => updateFormData("phone", e.target.value)}
                        className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                        placeholder="+91 98765 43210"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Investment Profile */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="investorType" className="text-slate-300">Investor Type *</Label>
                    <select
                      id="investorType"
                      value={formData.investorType}
                      onChange={(e) => updateFormData("investorType", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Type</option>
                      <option value="individual">Individual Investor</option>
                      <option value="hni">High Net Worth Individual</option>
                      <option value="family_office">Family Office</option>
                      <option value="institutional">Institutional Investor</option>
                      <option value="vc_fund">VC Fund</option>
                      <option value="pe_fund">PE Fund</option>
                    </select>
                  </div>
                  
                  <div>
                    <Label htmlFor="investmentCapacity" className="text-slate-300">Total Investment Capacity *</Label>
                    <select
                      id="investmentCapacity"
                      value={formData.investmentCapacity}
                      onChange={(e) => updateFormData("investmentCapacity", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Range</option>
                      <option value="130K-650K">$130K - $650K</option>
                      <option value="650K-1.3M">$650K - $1.3M</option>
                      <option value="1.3M-6.5M">$1.3M - $6.5M</option>
                      <option value="6.5M-13M">$6.5M - $13M</option>
                      <option value="13M-65M">$13M - $65M</option>
                      <option value="65M+">$65M+</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="minInvestment" className="text-slate-300">Min Investment per Deal *</Label>
                      <select
                        id="minInvestment"
                        value={formData.minInvestment}
                        onChange={(e) => updateFormData("minInvestment", e.target.value)}
                        className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="">Select Amount</option>
                        <option value="65K">$65K</option>
                        <option value="130K">$130K</option>
                        <option value="325K">$325K</option>
                        <option value="650K">$650K</option>
                        <option value="1.3M">$1.3M</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="maxInvestment" className="text-slate-300">Max Investment per Deal *</Label>
                      <select
                        id="maxInvestment"
                        value={formData.maxInvestment}
                        onChange={(e) => updateFormData("maxInvestment", e.target.value)}
                        className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="">Select Amount</option>
                        <option value="650K">$650K</option>
                        <option value="1.3M">$1.3M</option>
                        <option value="2.6M">$2.6M</option>
                        <option value="6.5M">$6.5M</option>
                        <option value="13M+">$13M+</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="riskTolerance" className="text-slate-300">Risk Tolerance *</Label>
                    <select
                      id="riskTolerance"
                      value={formData.riskTolerance}
                      onChange={(e) => updateFormData("riskTolerance", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Risk Level</option>
                      <option value="conservative">Conservative (Low Risk)</option>
                      <option value="moderate">Moderate (Medium Risk)</option>
                      <option value="aggressive">Aggressive (High Risk)</option>
                      <option value="very_aggressive">Very Aggressive (Very High Risk)</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Step 3: Investment Preferences */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <div>
                    <Label className="text-slate-300 mb-3 block">Preferred Industries *</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {industries.map((industry) => (
                        <label key={industry} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.preferredIndustries.includes(industry)}
                            onChange={() => toggleArrayValue("preferredIndustries", industry)}
                            className="rounded border-slate-600 bg-slate-700/50 text-emerald-600 focus:ring-emerald-500"
                          />
                          <span className="text-slate-300 text-sm">{industry}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-slate-300 mb-3 block">Preferred Investment Stages *</Label>
                    <div className="space-y-2">
                      {stages.map((stage) => (
                        <label key={stage} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.preferredStages.includes(stage)}
                            onChange={() => toggleArrayValue("preferredStages", stage)}
                            className="rounded border-slate-600 bg-slate-700/50 text-emerald-600 focus:ring-emerald-500"
                          />
                          <span className="text-slate-300 text-sm">{stage}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="geographicPreference" className="text-slate-300">Geographic Preference</Label>
                      <select
                        id="geographicPreference"
                        value={formData.geographicPreference}
                        onChange={(e) => updateFormData("geographicPreference", e.target.value)}
                        className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="">No Preference</option>
                        <option value="local">Local (Same City)</option>
                        <option value="regional">Regional (Same State)</option>
                        <option value="national">National (India)</option>
                        <option value="international">International</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="investmentHorizon" className="text-slate-300">Investment Horizon</Label>
                      <select
                        id="investmentHorizon"
                        value={formData.investmentHorizon}
                        onChange={(e) => updateFormData("investmentHorizon", e.target.value)}
                        className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="">Select Timeline</option>
                        <option value="1-2years">1-2 years</option>
                        <option value="3-5years">3-5 years</option>
                        <option value="5-7years">5-7 years</option>
                        <option value="7+years">7+ years</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="expectedReturns" className="text-slate-300">Expected Annual Returns</Label>
                    <select
                      id="expectedReturns"
                      value={formData.expectedReturns}
                      onChange={(e) => updateFormData("expectedReturns", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Range</option>
                      <option value="10-15%">10-15%</option>
                      <option value="15-20%">15-20%</option>
                      <option value="20-25%">20-25%</option>
                      <option value="25-30%">25-30%</option>
                      <option value="30%+">30%+</option>
                    </select>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-between mt-8"
        >
          <div>
            {currentStep > 1 ? (
              <Button variant="outline" onClick={prevStep} className="border-slate-600 text-white hover:bg-slate-700">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
            ) : (
              <Link href="/investor">
                <Button variant="outline" className="border-slate-600 text-white hover:bg-slate-700">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Investor Page
                </Button>
              </Link>
            )}
          </div>
          
          <div>
            {currentStep < totalSteps ? (
              <Button onClick={nextStep} className="bg-emerald-600 hover:bg-emerald-700">
                Next Step
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Link href="/investor/dashboard">
                <Button className="bg-emerald-600 hover:bg-emerald-700">
                  Complete Registration
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
