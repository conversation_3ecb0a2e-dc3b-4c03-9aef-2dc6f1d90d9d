"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    BarChart3,
    Building,
    Calendar,
    DollarSign,
    Eye,
    Filter,
    Heart,
    MapPin,
    MessageCircle,
    Search,
    TrendingUp
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function InvestorDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  // Mock data - in real app, this would come from API
  const portfolioStats = {
    totalInvestments: 12,
    totalInvested: "$3.1M",
    activeDeals: 8,
    avgReturn: "18.5%"
  };

  const dealPipeline = [
    {
      id: 1,
      companyName: "TechCorp Solutions",
      industry: "Technology",
      location: "Bangalore",
      fundingGoal: "$650K",
      financialScore: 78,
      yearEstablished: 2020,
      employees: "25-50",
      revenue: "$3.1M",
      status: "new",
      description: "AI-powered customer service automation platform",
      tags: ["SaaS", "AI", "B2B"],
      interested: 15,
      viewed: 234
    },
    {
      id: 2,
      companyName: "GreenEnergy Pvt Ltd",
      industry: "Renewable Energy",
      location: "Pune",
      fundingGoal: "$1.6M",
      financialScore: 85,
      yearEstablished: 2018,
      employees: "50-100",
      revenue: "$7.5M",
      status: "trending",
      description: "Solar panel manufacturing and installation services",
      tags: ["CleanTech", "Manufacturing", "B2B"],
      interested: 28,
      viewed: 456
    },
    {
      id: 3,
      companyName: "HealthTech Innovations",
      industry: "Healthcare",
      location: "Mumbai",
      fundingGoal: "$975K",
      financialScore: 72,
      yearEstablished: 2021,
      employees: "10-25",
      revenue: "$2.3M",
      status: "hot",
      description: "Telemedicine platform for rural healthcare access",
      tags: ["HealthTech", "B2C", "Social Impact"],
      interested: 22,
      viewed: 189
    }
  ];

  const recentActivity = [
    { id: 1, type: "view", message: "You viewed TechCorp Solutions", time: "2 hours ago" },
    { id: 2, type: "interest", message: "You showed interest in GreenEnergy Pvt Ltd", time: "5 hours ago" },
    { id: 3, type: "message", message: "New message from HealthTech Innovations", time: "1 day ago" },
    { id: 4, type: "match", message: "3 new deals match your criteria", time: "2 days ago" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-100 text-blue-700 border-blue-200";
      case "trending": return "bg-green-100 text-green-700 border-green-200";
      case "hot": return "bg-red-100 text-red-700 border-red-200";
      default: return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="investor" />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-h1 text-black mb-2">Welcome back, {user?.name || 'Investor'}</h1>
              <p className="text-body text-gray-600">Discover and manage your investment opportunities</p>
            </div>
            <Badge className="bg-green-100 text-green-700 border-green-200">
              Professional Investor
            </Badge>
          </div>
        </motion.div>

        {/* Portfolio Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <div className="text-h2 text-black mb-1">{portfolioStats.totalInvested}</div>
                <p className="text-body-small text-gray-600">Total Invested</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Building className="w-6 h-6 text-black" />
                  </div>
                </div>
                <div className="text-h2 text-black mb-1">{portfolioStats.totalInvestments}</div>
                <p className="text-body-small text-gray-600">Total Investments</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-black" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-black mb-1">{portfolioStats.activeDeals}</div>
                <p className="text-gray-600 text-sm">Active Deals</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-black" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-black mb-1">{portfolioStats.avgReturn}</div>
                <p className="text-gray-600 text-sm">Avg Return</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Deal Pipeline */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="lg:col-span-3"
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-black flex items-center">
                      <Search className="w-5 h-5 mr-2 text-black" />
                      Deal Pipeline
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      Discover investment opportunities matching your criteria
                    </CardDescription>
                  </div>
                  <Button variant="outline" size="sm" className="border-gray-300 text-black hover:bg-gray-50">
                    <Filter className="w-4 h-4 mr-2" />
                    Filters
                  </Button>
                </div>
                
                {/* Search Bar */}
                <div className="flex space-x-4 mt-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search companies, industries, or keywords..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 bg-white border-gray-300 text-black"
                    />
                  </div>
                  <select
                    value={selectedFilter}
                    onChange={(e) => setSelectedFilter(e.target.value)}
                    className="bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                  >
                    <option value="all">All Industries</option>
                    <option value="technology">Technology</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="energy">Energy</option>
                    <option value="manufacturing">Manufacturing</option>
                  </select>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {dealPipeline.map((deal) => (
                    <div
                      key={deal.id}
                      className="p-6 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-400 transition-all duration-300"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-h4 text-black">{deal.companyName}</h3>
                            <Badge className={getStatusColor(deal.status)}>
                              {deal.status}
                            </Badge>
                          </div>
                          <p className="text-gray-700 mb-3">{deal.description}</p>
                          <div className="flex flex-wrap gap-2 mb-3">
                            {deal.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="border-gray-300 text-gray-600 text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-black mb-1">{deal.fundingGoal}</div>
                          <p className="text-gray-600 text-sm">Funding Goal</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-gray-600 text-xs">Financial Score</p>
                          <p className="text-black font-semibold">{deal.financialScore}/100</p>
                        </div>
                        <div>
                          <p className="text-gray-600 text-xs">Revenue</p>
                          <p className="text-black font-semibold">{deal.revenue}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 text-xs">Employees</p>
                          <p className="text-black font-semibold">{deal.employees}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 text-xs">Location</p>
                          <p className="text-black font-semibold flex items-center">
                            <MapPin className="w-3 h-3 mr-1" />
                            {deal.location}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-gray-600 text-sm">
                          <span className="flex items-center">
                            <Eye className="w-4 h-4 mr-1" />
                            {deal.viewed} views
                          </span>
                          <span className="flex items-center">
                            <Heart className="w-4 h-4 mr-1" />
                            {deal.interested} interested
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" className="border-gray-300 text-black hover:bg-gray-50">
                            <Heart className="w-4 h-4 mr-2" />
                            Save
                          </Button>
                          <Button variant="outline" size="sm" className="border-gray-300 text-black hover:bg-gray-50">
                            <MessageCircle className="w-4 h-4 mr-2" />
                            Contact
                          </Button>
                          <Link href={`/investor/dashboard/deal/${deal.id}`}>
                            <Button size="sm" className="bg-black hover:bg-gray-800 text-white">
                              View Details
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-black" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.type === "view" ? "bg-black" :
                        activity.type === "interest" ? "bg-black" :
                        activity.type === "message" ? "bg-black" : "bg-black"
                      }`} />
                      <div className="flex-1">
                        <p className="text-black text-sm">{activity.message}</p>
                        <p className="text-gray-600 text-xs">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
