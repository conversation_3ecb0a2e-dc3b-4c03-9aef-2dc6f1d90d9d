"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    BarChart3,
    Building,
    Calendar,
    DollarSign,
    Eye,
    Filter,
    Heart,
    MapPin,
    MessageCircle,
    Search,
    TrendingUp
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function InvestorDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  // Mock data - in real app, this would come from API
  const portfolioStats = {
    totalInvestments: 12,
    totalInvested: "$3.1M",
    activeDeals: 8,
    avgReturn: "18.5%"
  };

  const dealPipeline = [
    {
      id: 1,
      companyName: "TechCorp Solutions",
      industry: "Technology",
      location: "Bangalore",
      fundingGoal: "$650K",
      financialScore: 78,
      yearEstablished: 2020,
      employees: "25-50",
      revenue: "$3.1M",
      status: "new",
      description: "AI-powered customer service automation platform",
      tags: ["SaaS", "AI", "B2B"],
      interested: 15,
      viewed: 234
    },
    {
      id: 2,
      companyName: "GreenEnergy Pvt Ltd",
      industry: "Renewable Energy",
      location: "Pune",
      fundingGoal: "$1.6M",
      financialScore: 85,
      yearEstablished: 2018,
      employees: "50-100",
      revenue: "$7.5M",
      status: "trending",
      description: "Solar panel manufacturing and installation services",
      tags: ["CleanTech", "Manufacturing", "B2B"],
      interested: 28,
      viewed: 456
    },
    {
      id: 3,
      companyName: "HealthTech Innovations",
      industry: "Healthcare",
      location: "Mumbai",
      fundingGoal: "$975K",
      financialScore: 72,
      yearEstablished: 2021,
      employees: "10-25",
      revenue: "$2.3M",
      status: "hot",
      description: "Telemedicine platform for rural healthcare access",
      tags: ["HealthTech", "B2C", "Social Impact"],
      interested: 22,
      viewed: 189
    }
  ];

  const recentActivity = [
    { id: 1, type: "view", message: "You viewed TechCorp Solutions", time: "2 hours ago" },
    { id: 2, type: "interest", message: "You showed interest in GreenEnergy Pvt Ltd", time: "5 hours ago" },
    { id: 3, type: "message", message: "New message from HealthTech Innovations", time: "1 day ago" },
    { id: 4, type: "match", message: "3 new deals match your criteria", time: "2 days ago" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "trending": return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30";
      case "hot": return "bg-red-500/20 text-red-400 border-red-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <AppHeader variant="investor" />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-h1 text-white mb-2">Welcome back, {user?.name || 'Investor'}</h1>
              <p className="text-body text-slate-400">Discover and manage your investment opportunities</p>
            </div>
            <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
              Professional Investor
            </Badge>
          </div>
        </motion.div>

        {/* Portfolio Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-emerald-400" />
                  </div>
                </div>
                <div className="text-h2 text-white mb-1">{portfolioStats.totalInvested}</div>
                <p className="text-body-small text-slate-400">Total Invested</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Building className="w-6 h-6 text-blue-400" />
                  </div>
                </div>
                <div className="text-h2 text-white mb-1">{portfolioStats.totalInvestments}</div>
                <p className="text-body-small text-slate-400">Total Investments</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-amber-500/20 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-amber-400" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">{portfolioStats.activeDeals}</div>
                <p className="text-slate-400 text-sm">Active Deals</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-purple-400" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">{portfolioStats.avgReturn}</div>
                <p className="text-slate-400 text-sm">Avg Return</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Deal Pipeline */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="lg:col-span-3"
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-white flex items-center">
                      <Search className="w-5 h-5 mr-2 text-emerald-400" />
                      Deal Pipeline
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Discover investment opportunities matching your criteria
                    </CardDescription>
                  </div>
                  <Button variant="outline" size="sm" className="border-slate-600 text-white">
                    <Filter className="w-4 h-4 mr-2" />
                    Filters
                  </Button>
                </div>
                
                {/* Search Bar */}
                <div className="flex space-x-4 mt-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                    <Input
                      placeholder="Search companies, industries, or keywords..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                    />
                  </div>
                  <select
                    value={selectedFilter}
                    onChange={(e) => setSelectedFilter(e.target.value)}
                    className="bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                  >
                    <option value="all">All Industries</option>
                    <option value="technology">Technology</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="energy">Energy</option>
                    <option value="manufacturing">Manufacturing</option>
                  </select>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {dealPipeline.map((deal) => (
                    <div
                      key={deal.id}
                      className="p-6 bg-slate-700/30 rounded-lg border border-slate-600/30 hover:border-emerald-500/50 transition-all duration-300"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-h4 text-white">{deal.companyName}</h3>
                            <Badge className={getStatusColor(deal.status)}>
                              {deal.status}
                            </Badge>
                          </div>
                          <p className="text-slate-300 mb-3">{deal.description}</p>
                          <div className="flex flex-wrap gap-2 mb-3">
                            {deal.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="border-slate-600 text-slate-400 text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-emerald-400 mb-1">{deal.fundingGoal}</div>
                          <p className="text-slate-400 text-sm">Funding Goal</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-slate-400 text-xs">Financial Score</p>
                          <p className="text-white font-semibold">{deal.financialScore}/100</p>
                        </div>
                        <div>
                          <p className="text-slate-400 text-xs">Revenue</p>
                          <p className="text-white font-semibold">{deal.revenue}</p>
                        </div>
                        <div>
                          <p className="text-slate-400 text-xs">Employees</p>
                          <p className="text-white font-semibold">{deal.employees}</p>
                        </div>
                        <div>
                          <p className="text-slate-400 text-xs">Location</p>
                          <p className="text-white font-semibold flex items-center">
                            <MapPin className="w-3 h-3 mr-1" />
                            {deal.location}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-slate-400 text-sm">
                          <span className="flex items-center">
                            <Eye className="w-4 h-4 mr-1" />
                            {deal.viewed} views
                          </span>
                          <span className="flex items-center">
                            <Heart className="w-4 h-4 mr-1" />
                            {deal.interested} interested
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" className="border-slate-600 text-white hover:bg-slate-700">
                            <Heart className="w-4 h-4 mr-2" />
                            Save
                          </Button>
                          <Button variant="outline" size="sm" className="border-slate-600 text-white hover:bg-slate-700">
                            <MessageCircle className="w-4 h-4 mr-2" />
                            Contact
                          </Button>
                          <Link href={`/investor/dashboard/deal/${deal.id}`}>
                            <Button size="sm" className="bg-emerald-600 hover:bg-emerald-700">
                              View Details
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-blue-400" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.type === "view" ? "bg-blue-400" :
                        activity.type === "interest" ? "bg-emerald-400" :
                        activity.type === "message" ? "bg-amber-400" : "bg-purple-400"
                      }`} />
                      <div className="flex-1">
                        <p className="text-white text-sm">{activity.message}</p>
                        <p className="text-slate-400 text-xs">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
