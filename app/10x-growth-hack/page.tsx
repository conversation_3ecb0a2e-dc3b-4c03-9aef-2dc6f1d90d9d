"use client";

import AppHeader from "@/components/navigation/AppHeader";
import Footer from "@/components/navigation/Footer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRight, BarChart3, Check, Search, Target, TrendingUp, Users } from "lucide-react";
import Link from "next/link";

export default function GrowthHackPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100/20 to-gray-200/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-200/20 via-transparent to-transparent" />
        
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge variant="secondary" className="mb-6 px-6 py-2 text-sm font-medium bg-gray-100 text-black border-gray-200">
              🎯 Exclusive Program
            </Badge>
            <h1 className="text-5xl sm:text-7xl font-bold text-black mb-8 leading-tight">
              10X Growth Acceleration
              <span className="block text-black">
                Program
              </span>
            </h1>
            <p className="text-body-large text-gray-600 mb-12 max-w-3xl mx-auto">
              An exclusive intensive program designed to accelerate your business growth by 10X. Get personalized strategies, expert mentorship, and access to capital
            </p>
            <div className="flex justify-center">
              <Button size="lg" className="px-8 py-4 text-button bg-black hover:bg-gray-800 text-white rounded-full">
                Apply for Program
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Program Overview */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>What Makes This Program Distinct?</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Unlike generic business accelerators, our 10X Growth Program is designed exclusively for established SMEs seeking structured, sustainable, and rapid scale-up.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Personalized Strategy */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Target className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Personalized Growth Strategy</h3>
                  <p className="text-gray-600 mb-6">
                    Custom-built growth plan based on your business model, market position, and strategic objectives
                  </p>
                  <ul className="space-y-2 text-sm text-gray-700 text-left">
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Comprehensive business analysis</li>
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Market opportunity mapping</li>
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Aggressive execution roadmap</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* Expert Mentorship */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Users className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Expert-Led Advisory</h3>
                  <p className="text-gray-600 mb-6">
                    Benefit from direct guidance through structured sessions with seasoned professionals who have successfully scaled organizations across diverse industries.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-700 text-left">
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Weekly strategic mentorship sessions</li>
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Industry-specific insights</li>
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Need based advisory support</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* Investor Network */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <TrendingUp className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Strategic Capital Readiness</h3>
                  <p className="text-gray-600 mb-6">
                    Position your business for long-term expansion by refining governance, enhancing operational efficiency, and preparing for high-value partnerships.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-700 text-left">
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Business case refinement</li>
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Support with growth negotiations</li>
                    <li className="flex items-center"><Check className="w-4 h-4 text-black mr-2" />Capital raise support</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Program Timeline */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>10x Transformation Journey</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              A structured approach to achieving 10X growth
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                phase: "Phase 1",
                title: "Foundation & Analysis",
                description: "Establish a strong base through in-depth business diagnostics, market opportunity assessment, and strategic growth planning.",
                details: ["Comprehensive business and market analysis", "Identification of growth levers and risks", "Development of a tailored growth roadmap"],
                icon: Search
              },
              {
                phase: "Phase 2",
                title: "Implementation & Optimization",
                description: "Translate strategy into action by executing growth initiatives, streamlining operations, and embedding performance tracking systems.",
                details: ["Rollout of key growth initiatives", "Operational and financial optimization", "Ongoing advisory and progress monitoring"],
                icon: BarChart3
              },
              {
                phase: "Phase 3",
                title: "Scale & Capital Readiness",
                description: "Support the company in preparing for large-scale growth and capital engagement by strengthening governance, refining business cases, and positioning for external investment or partnerships.",
                details: ["Business case refinement", "Scalability planning", "Strategic capital and partnership readiness"],
                icon: TrendingUp
              }
            ].map((phase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mb-6">
                      <phase.icon className="w-8 h-8 text-black" />
                    </div>
                    <div className="text-sm font-bold text-black mb-2">{phase.phase}</div>
                    <h3 className="text-xl font-bold text-black mb-4">{phase.title}</h3>
                    <p className="text-gray-600 mb-6">{phase.description}</p>
                    <ul className="space-y-2 text-sm text-gray-700">
                      {phase.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-start">
                          <Check className="w-4 h-4 text-black mr-2 mt-0.5 flex-shrink-0" />
                          {detail}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Metrics - HIDDEN */}
      {/*
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Program Results</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg mb-12">
              Real results from businesses that completed our 10X Growth Hack program.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto bg-white rounded-3xl py-12 px-8 shadow-lg border border-gray-100">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-black mb-2">10X</div>
              <div className="text-gray-600">Average Revenue Growth</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-black mb-2">85%</div>
              <div className="text-gray-600">Secured Funding</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-black mb-2">90</div>
              <div className="text-gray-600">Days to Results</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center"
            >
              <div className="text-4xl font-bold text-black mb-2">50+</div>
              <div className="text-gray-600">Success Stories</div>
            </motion.div>
          </div>
        </div>
      </section>
      */}

      {/* Qualification Criteria - HIDDEN */}
      {/*
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Are You Qualified?</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              This exclusive program is designed for serious entrepreneurs ready to scale rapidly.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <Card className="bg-white border-gray-200">
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-bold text-black mb-4 flex items-center">
                      <Check className="w-6 h-6 mr-2" />
                      You&apos;re a Perfect Fit If:
                    </h3>
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-black mr-3 mt-0.5 flex-shrink-0" />
                        Annual revenue of $650K+ and growing
                      </li>
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-black mr-3 mt-0.5 flex-shrink-0" />
                        Financial health score of 60+ on 10xCFO
                      </li>
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-black mr-3 mt-0.5 flex-shrink-0" />
                        Committed to 10+ hours/week for 90 days
                      </li>
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-black mr-3 mt-0.5 flex-shrink-0" />
                        Ready to implement aggressive growth strategies
                      </li>
                      <li className="flex items-start">
                        <Star className="w-5 h-5 text-black mr-3 mt-0.5 flex-shrink-0" />
                        Open to external investment/partnerships
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-red-600 mb-4 flex items-center">
                      <Clock className="w-6 h-6 mr-2" />
                      This Program Isn&apos;t For You If:
                    </h3>
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        You&apos;re looking for quick fixes or shortcuts
                      </li>
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        Your business is pre-revenue or struggling
                      </li>
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        You can&apos;t commit significant time and resources
                      </li>
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        You&apos;re not ready for rapid change and growth
                      </li>
                      <li className="flex items-start">
                        <span className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0">✗</span>
                        You prefer traditional, slow-growth approaches
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      */}

      {/* Application CTA */}
      <section className="py-20 px-4 bg-black">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🚀 Limited Spots Available
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">Ready to 10X Your Business?</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Only 20 businesses are accepted per cohort. Applications are reviewed within 48 hours.
            </p>
            <div className="flex justify-center">
              <Link href="/discovery-call">
                <Button size="lg" className="text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300 !bg-white !text-black hover:!bg-gray-100">
                  Apply
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
            <p className="text-gray-300 text-sm mt-4 opacity-80">
              * 100% money-back guarantee if you don&apos;t see measurable results in 90 days
            </p>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
