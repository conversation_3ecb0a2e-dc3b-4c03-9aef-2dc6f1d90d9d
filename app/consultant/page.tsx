"use client";

import AppHeader from "@/components/navigation/AppHeader";
import Footer from "@/components/navigation/Footer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRight, Award, BarChart3, DollarSign, Target, TrendingUp, Users } from "lucide-react";
import Link from "next/link";

export default function ConsultantOverview() {
  const benefits = [
    {
      icon: Users,
      title: "Client Origination",
      description: "Leverage CFOx's platform to identify, engage, and onboard SMEs seeking structured growth and financial advisory support.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: DollarSign,
      title: "Capital Enablement",
      description: "Facilitate access to capital by connecting SMEs with a curated network of investors, lenders, and financial institutions.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: BarChart3,
      title: "Outcome-Driven Advisory",
      description: "Utilize CFOx's analytical tools and dashboards to deliver measurable improvements in performance, governance, and growth outcomes.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: Award,
      title: "Professional Accreditation",
      description: "Attain CFOx certification, enhancing your credentials and positioning you as a trusted advisor to SMEs and investors alike.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: Target,
      title: "Market Connectivity",
      description: "Gain access to a pipeline of SMEs actively seeking advisory services, as well as CFOx's broader ecosystem of strategic partners.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: TrendingUp,
      title: "Practice Expansion",
      description: "Scale your consulting practice with the support of CFOx's technology, resources, and structured engagement opportunities.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100/20 to-gray-200/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-200/20 via-transparent to-transparent" />
        
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge variant="secondary" className="mb-6 px-6 py-2 text-sm font-medium bg-gray-100 text-black border-gray-200">
              🎯 For Consultants
            </Badge>
            <h1 className="text-5xl sm:text-7xl font-bold text-black mb-8 leading-tight">
              Help SMEs Grow &
              <span className="block text-black">
                Earn Rewards
              </span>
            </h1>
            <p className="text-body-large text-gray-600 mb-12 max-w-3xl mx-auto">
              Join our network of certified consultants guiding SMEs to streamline their business, attract capital and accelerate growth.
            </p>
            <div className="flex justify-center">
              <Link href="/consultant/signup">
                <Button size="lg" className="px-8 py-4 text-button bg-black hover:bg-gray-800 text-white rounded-full">
                  Join as Consultant <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Why Partner with CFOx?</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              CFOx offers a collaborative platform where consultants can create value: sourcing clients, facilitating capital, and delivering measurable growth outcomes.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className={`w-16 h-16 ${benefit.bgColor} rounded-2xl flex items-center justify-center mb-6`}>
                      <benefit.icon className={`w-8 h-8 ${benefit.color}`} />
                    </div>
                    <h3 className="text-xl font-bold text-black mb-4">{benefit.title}</h3>
                    <p className="text-gray-600">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Earning Potential - HIDDEN */}
      {/*
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Earning Potential</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Multiple revenue streams to maximize your consulting income.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto bg-white rounded-3xl py-12 px-8 shadow-lg border border-gray-100">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-white border-gray-200 text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-black mb-2">$325</div>
                  <div className="text-gray-700 mb-4">Per Successful SME Referral</div>
                  <p className="text-gray-600 text-sm">
                    Earn when SMEs you help get funded through our platform
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-white border-gray-200 text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-black mb-2">$65</div>
                  <div className="text-gray-700 mb-4">Per Financial Health Improvement</div>
                  <p className="text-gray-600 text-sm">
                    Bonus for helping SMEs improve their 10xCFO score by 20+ points
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-white border-gray-200 text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-black mb-2">$2.6K+</div>
                  <div className="text-gray-700 mb-4">Monthly Potential</div>
                  <p className="text-gray-600 text-sm">
                    Top consultants earn $2.6K+ per month through our platform
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
      */}

      {/* CTA Section */}
      <section className="py-20 px-4 bg-black">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🚀 Join Our Network
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">Ready to Start Consulting?</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join hundreds of consultants who are already helping SMEs grow and earning rewards through CFOx.
            </p>
            <div className="flex justify-center">
              <Link href="/consultant/signup">
                <Button size="lg" className="text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300 !bg-white !text-black hover:!bg-gray-100">
                  Apply as Consultant <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
