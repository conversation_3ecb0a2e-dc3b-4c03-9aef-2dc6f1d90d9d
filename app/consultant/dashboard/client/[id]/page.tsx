// Server component for static generation
import ClientDetailClient from './ClientDetailClient';

// Generate static params for static export
// export async function generateStaticParams() {
//   // Generate some sample client IDs for static export
//   return [
//     { id: '1' },
//     { id: '2' },
//     { id: '3' },
//     { id: '4' },
//     { id: '5' },
//   ];
// }

// Server component wrapper
export default function ClientDetailPage({ params }: { params: { id: string } }) {
  return <ClientDetailClient />;
}
