"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { BarChart3, DollarSign, TrendingUp, Users } from "lucide-react";
import { useState } from "react";

export default function ConsultantAnalytics() {
  const [timeRange, setTimeRange] = useState("30d");

  // Mock data - replace with actual API calls
  const stats = {
    totalRevenue: "$45,280",
    revenueChange: "+12.5%",
    activeClients: 8,
    clientsChange: "+2",
    completedProjects: 15,
    projectsChange: "+3",
    avgProjectValue: "$3,019",
    valueChange: "+8.2%"
  };

  const monthlyData = [
    { month: "Jan", revenue: 12500, clients: 5, projects: 3 },
    { month: "Feb", revenue: 15800, clients: 6, projects: 4 },
    { month: "Mar", revenue: 18200, clients: 7, projects: 5 },
    { month: "Apr", revenue: 22100, clients: 8, projects: 6 },
    { month: "May", revenue: 25600, clients: 9, projects: 7 },
    { month: "Jun", revenue: 28900, clients: 10, projects: 8 }
  ];

  const clientPerformance = [
    { name: "TechCorp Solutions", revenue: "$12,500", projects: 4, satisfaction: 95 },
    { name: "GreenEnergy Ltd", revenue: "$8,200", projects: 3, satisfaction: 92 },
    { name: "HealthTech Innovations", revenue: "$15,600", projects: 5, satisfaction: 98 },
    { name: "RetailCorp", revenue: "$6,800", projects: 2, satisfaction: 88 },
    { name: "FinanceFlow", revenue: "$9,400", projects: 3, satisfaction: 94 }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AppHeader variant="consultant" />
      
      {/* Back Navigation */}
      <BackButton href="/consultant/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-black mb-2">Analytics</h1>
            <p className="text-gray-600">Track your performance and business metrics</p>
          </div>
          <div className="flex gap-2">
            {["7d", "30d", "90d", "1y"].map((range) => (
              <Button
              key={range}
              onClick={() => setTimeRange(range)}
              variant={timeRange === range ? "primary-black" : "outline-gray"}
              size="sm"
            >
              {range}
            </Button>
            ))}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">Total Revenue</p>
                    <p className="text-2xl font-bold text-black">{stats.totalRevenue}</p>
                    <p className="text-green-600 text-sm">{stats.revenueChange} from last month</p>
                  </div>
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">Active Clients</p>
                    <p className="text-2xl font-bold text-black">{stats.activeClients}</p>
                    <p className="text-blue-600 text-sm">{stats.clientsChange} new this month</p>
                  </div>
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">Completed Projects</p>
                    <p className="text-2xl font-bold text-black">{stats.completedProjects}</p>
                    <p className="text-purple-600 text-sm">{stats.projectsChange} this month</p>
                  </div>
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">Avg Project Value</p>
                    <p className="text-2xl font-bold text-black">{stats.avgProjectValue}</p>
                    <p className="text-orange-600 text-sm">{stats.valueChange} increase</p>
                  </div>
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Revenue Chart */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black">Revenue Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {monthlyData.map((data, index) => (
                    <div key={data.month} className="flex items-center justify-between">
                      <span className="text-gray-600 text-sm w-12">{data.month}</span>
                      <div className="flex-1 mx-4">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${(data.revenue / 30000) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <span className="text-black text-sm font-medium w-20 text-right">
                        ${(data.revenue / 1000).toFixed(1)}k
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Client Performance */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black">Top Clients</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {clientPerformance.map((client, index) => (
                    <div key={client.name} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-black font-medium">{client.name}</h4>
                        <Badge variant="outline">
                          {client.satisfaction}% satisfaction
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Revenue: </span>
                          <span className="text-green-600 font-medium">{client.revenue}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Projects: </span>
                          <span className="text-black font-medium">{client.projects}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Performance Insights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-8"
        >
          <Card className="bg-white border-gray-200 shadow-sm">
  <CardHeader>
    <CardTitle className="text-black">Performance Insights</CardTitle>
  </CardHeader>
  <CardContent>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <TrendingUp className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-black font-semibold mb-2">Revenue Growth</h3>
        <p className="text-gray-600 text-sm">Your revenue has grown consistently over the past 6 months with a 12.5% increase this month.</p>
      </div>
      <div className="text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <Users className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-black font-semibold mb-2">Client Satisfaction</h3>
        <p className="text-gray-600 text-sm">Average client satisfaction is 93.4%, indicating strong service quality and client relationships.</p>
      </div>
      <div className="text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <BarChart3 className="w-8 h-8 text-purple-600" />
        </div>
        <h3 className="text-black font-semibold mb-2">Project Efficiency</h3>
        <p className="text-gray-600 text-sm">You&apos;ve completed 15 projects with an average value increase of 8.2% per project.</p>
      </div>
    </div>
  </CardContent>
</Card>
        </motion.div>
      </div>
    </div>
  );
}
