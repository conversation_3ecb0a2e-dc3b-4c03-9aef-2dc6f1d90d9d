import { cn } from "@/lib/utils";
import { fontStyles, getTypographyStyle } from "@/lib/fonts";
import { forwardRef } from "react";

type TypographyVariant = keyof typeof fontStyles;

interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  variant?: TypographyVariant;
  as?: keyof JSX.IntrinsicElements;
  children: React.ReactNode;
}

/**
 * Typography component with consistent Geist font styling
 * 
 * @example
 * <Typography variant="h1">Main Heading</Typography>
 * <Typography variant="body" as="p">Body text</Typography>
 * <Typography variant="caption" as="span">Small caption</Typography>
 */
export const Typography = forwardRef<HTMLElement, TypographyProps>(
  ({ variant = "body", as, className, children, ...props }, ref) => {
    const style = getTypographyStyle(variant);
    
    // Default element mapping based on variant
    const defaultElement = {
      h1: "h1",
      h2: "h2", 
      h3: "h3",
      h4: "h4",
      h5: "h5",
      h6: "h6",
      body: "p",
      bodyLarge: "p",
      bodySmall: "p",
      button: "span",
      label: "label",
      caption: "span",
    }[variant] as keyof JSX.IntrinsicElements;

    const Component = as || defaultElement;

    return (
      <Component
        ref={ref as any}
        className={cn(style.className, className)}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Typography.displayName = "Typography";

// Convenience components for common use cases
export const Heading1 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="h1" ref={ref} className={className} {...props} />
  )
);
Heading1.displayName = "Heading1";

export const Heading2 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="h2" ref={ref} className={className} {...props} />
  )
);
Heading2.displayName = "Heading2";

export const Heading3 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="h3" ref={ref} className={className} {...props} />
  )
);
Heading3.displayName = "Heading3";

export const Heading4 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="h4" ref={ref} className={className} {...props} />
  )
);
Heading4.displayName = "Heading4";

export const Heading5 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="h5" ref={ref} className={className} {...props} />
  )
);
Heading5.displayName = "Heading5";

export const Heading6 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="h6" ref={ref} className={className} {...props} />
  )
);
Heading6.displayName = "Heading6";

export const BodyText = forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="body" ref={ref} className={className} {...props} />
  )
);
BodyText.displayName = "BodyText";

export const BodyLarge = forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="bodyLarge" ref={ref} className={className} {...props} />
  )
);
BodyLarge.displayName = "BodyLarge";

export const BodySmall = forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="bodySmall" ref={ref} className={className} {...props} />
  )
);
BodySmall.displayName = "BodySmall";

export const Label = forwardRef<HTMLLabelElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="label" as="label" ref={ref} className={className} {...props} />
  )
);
Label.displayName = "Label";

export const Caption = forwardRef<HTMLSpanElement, Omit<TypographyProps, 'variant'>>(
  ({ className, ...props }, ref) => (
    <Typography variant="caption" as="span" ref={ref} className={className} {...props} />
  )
);
Caption.displayName = "Caption";
