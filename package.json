{"name": "10xcfo-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "./deploy-to-s3.sh"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^10.16.0", "lucide-react": "^0.263.1", "next": "14.2.5", "next-themes": "^0.4.6", "react": "18.3.1", "react-dom": "18.3.1", "react-dropzone": "^14.2.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.4.0", "recharts": "^2.8.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "zustand": "^4.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.11", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "eslint": "^9", "eslint-config-next": "14.2.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2"}}