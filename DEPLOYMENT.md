# 10xCFO Deployment Guide

This guide explains how to deploy the 10xCFO Next.js application to AWS S3 and CloudFront.

## 🏗️ Architecture

- **Frontend**: Next.js with static export
- **Storage**: AWS S3 bucket (`cfox`)
- **CDN**: AWS CloudFront distribution
- **Region**: Asia Pacific (Mumbai) - `ap-south-1`

## 📋 Prerequisites

1. **Node.js** (v18 or higher)
2. **npm** or **pnpm** package manager
3. **AWS CLI** (will be installed automatically if missing)
4. **AWS Account** with appropriate permissions

## 🔑 AWS Configuration

### Required AWS Permissions

Your AWS user/role needs the following permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket",
                "s3:PutBucketWebsite",
                "s3:PutBucketPolicy"
            ],
            "Resource": [
                "arn:aws:s3:::cfox",
                "arn:aws:s3:::cfox/*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "cloudfront:CreateInvalidation",
                "cloudfront:GetDistribution"
            ],
            "Resource": "arn:aws:cloudfront::************:distribution/EAHXVWDND4JQE"
        }
    ]
}
```

## 🚀 Quick Deployment

### Option 1: Automated Setup + Deployment

```bash
# 1. Setup AWS credentials and verify access
./setup-aws.sh

# 2. Deploy to S3 and CloudFront
./deploy-to-s3.sh
```

### Option 2: Manual AWS Configuration

```bash
# Configure AWS credentials manually
aws configure

# Deploy directly
./deploy-to-s3.sh
```

## 📁 Project Structure

```
CFOx-website/
├── app/                    # Next.js app directory
├── components/            # React components
├── public/               # Static assets
├── out/                  # Build output (generated)
├── deploy-to-s3.sh      # Deployment script
├── setup-aws.sh         # AWS setup script
└── next.config.ts       # Next.js configuration
```

## ⚙️ Configuration Details

### Next.js Configuration

The project is configured for static export in `next.config.ts`:

```typescript
const nextConfig: NextConfig = {
  output: 'export',           // Enable static export
  trailingSlash: true,        // Add trailing slashes
  images: {
    unoptimized: true,        // Disable image optimization
  },
  assetPrefix: 'https://d2tcjdwexogrvt.cloudfront.net', // CloudFront URL
};
```

### AWS Resources

- **S3 Bucket**: `cfox`
- **CloudFront Distribution**: `EAHXVWDND4JQE`
- **CloudFront Domain**: `d2tcjdwexogrvt.cloudfront.net`
- **Region**: `ap-south-1`

## 🔄 Deployment Process

The deployment script performs the following steps:

1. **Validation**
   - Check AWS CLI installation
   - Verify AWS credentials
   - Confirm S3 bucket access

2. **Build**
   - Install dependencies (if needed)
   - Build Next.js application
   - Generate static export in `out/` directory

3. **Upload**
   - Upload static assets with long cache headers (1 year)
   - Upload images with medium cache headers (1 day)
   - Upload HTML files with no cache headers

4. **Configuration**
   - Configure S3 bucket for static website hosting
   - Set public read permissions
   - Invalidate CloudFront cache

## 🌐 URLs After Deployment

- **S3 Website**: `http://cfox.s3-website.ap-south-1.amazonaws.com`
- **CloudFront CDN**: `https://d2tcjdwexogrvt.cloudfront.net`
- **S3 Console**: [S3 Bucket Management](https://s3.console.aws.amazon.com/s3/buckets/cfox)
- **CloudFront Console**: [CloudFront Distribution](https://console.aws.amazon.com/cloudfront/v3/home?region=us-east-1#/distributions/EAHXVWDND4JQE)

## 🐛 Troubleshooting

### Common Issues

1. **AWS CLI not found**
   ```bash
   # Install AWS CLI
   brew install awscli  # macOS
   pip install awscli   # Python
   ```

2. **Permission denied**
   ```bash
   # Make scripts executable
   chmod +x deploy-to-s3.sh setup-aws.sh
   ```

3. **Build fails**
   ```bash
   # Clear cache and reinstall dependencies
   rm -rf node_modules .next out
   npm install  # or pnpm install
   ```

4. **S3 bucket access denied**
   - Verify AWS credentials: `aws sts get-caller-identity`
   - Check IAM permissions
   - Ensure bucket exists and is in correct region

5. **CloudFront invalidation fails**
   - Check CloudFront permissions
   - Verify distribution ID is correct
   - Wait for previous invalidations to complete

### Cache Headers

- **Static Assets** (`_next/static/*`): 1 year cache
- **Images**: 1 day cache
- **HTML Files**: No cache (always fresh)
- **Other Files**: 1 hour cache

## 📊 Monitoring

After deployment, monitor:

- **S3 Bucket Size**: Check storage usage
- **CloudFront Metrics**: Monitor cache hit ratio
- **Build Times**: Track deployment duration
- **Error Rates**: Monitor 4xx/5xx responses

## 🔄 CI/CD Integration

To integrate with CI/CD pipelines:

```bash
# Set environment variables
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret

# Run deployment
./deploy-to-s3.sh
```

## 📞 Support

For deployment issues:

1. Check the deployment logs
2. Verify AWS permissions
3. Test with `./setup-aws.sh` first
4. Check CloudFront distribution status

---

**Note**: CloudFront cache invalidation may take 10-15 minutes to propagate globally.
